import React, { useEffect, useState, useMemo } from "react";
import { useAtom } from "jotai";
import { staticContentsAtom } from "@/lib/atom";

export type AuthenticationPhase =
  | "initializing"
  | "validating_session"
  | "processing_credentials"
  | "fetching_user_data"
  | "preparing_dashboard"
  | "redirecting";

interface AuthProgressMessage {
  title: string;
  description: string;
  operation: string; // Describes the actual operation being performed
}

interface DynamicAuthProgressProps {
  isIframe: boolean;
  onPhaseChange?: (phase: AuthenticationPhase) => void;
  onComplete?: () => void;
  currentOperation?: string; // Optional prop to override current operation
}

const DynamicAuthProgress: React.FC<DynamicAuthProgressProps> = ({
  isIframe,
  onPhaseChange,
  onComplete,
  currentOperation,
}) => {
  const [staticContents] = useAtom<any>(staticContentsAtom);
  const [currentPhase, setCurrentPhase] =
    useState<AuthenticationPhase>("initializing");
  const [progress, setProgress] = useState(0);
  const [currentOperationText, setCurrentOperationText] = useState("");

  // Define operation-specific authentication phases with actual operations
  const authPhases = useMemo(
    () => ({
      initializing: {
        title:
          staticContents?.authentication?.initializing_title || "Initializing",
        description: isIframe
          ? staticContents?.authentication?.initializing_iframe ||
            "Setting up secure communication channel..."
          : staticContents?.authentication?.initializing_direct ||
            "Preparing authentication environment...",
        operation: isIframe
          ? "Waiting for authentication data"
          : "Checking existing session",
        percentage: 15,
      },
      validating_session: {
        title:
          staticContents?.authentication?.validating_title ||
          "Validating Session",
        description: isIframe
          ? staticContents?.authentication?.validating_iframe ||
            "Processing authentication tokens from parent application..."
          : staticContents?.authentication?.validating_direct ||
            "Verifying existing authentication tokens...",
        operation: isIframe
          ? "Validating received tokens"
          : "Checking AWS Cognito session",
        percentage: 35,
      },
      processing_credentials: {
        title:
          staticContents?.authentication?.processing_title ||
          "Processing Credentials",
        description: isIframe
          ? staticContents?.authentication?.processing_iframe ||
            "Setting up secure session with provided credentials..."
          : staticContents?.authentication?.processing_direct ||
            "Establishing secure authentication session...",
        operation: isIframe
          ? "Setting up Cognito session"
          : "Signing in with credentials",
        percentage: 55,
      },
      fetching_user_data: {
        title:
          staticContents?.authentication?.fetching_title || "Loading User Data",
        description:
          staticContents?.authentication?.fetching_desc ||
          "Retrieving your application details and user information...",
        operation: "Fetching student details and application data",
        percentage: 75,
      },
      preparing_dashboard: {
        title:
          staticContents?.authentication?.preparing_title ||
          "Preparing Dashboard",
        description:
          staticContents?.authentication?.preparing_desc ||
          "Setting up your personalized application dashboard...",
        operation: "Configuring user interface and preferences",
        percentage: 90,
      },
      redirecting: {
        title:
          staticContents?.authentication?.redirecting_title || "Almost Ready",
        description:
          staticContents?.authentication?.redirecting_desc ||
          "Taking you to your application now...",
        operation: "Navigating to application",
        percentage: 100,
      },
    }),
    [staticContents, isIframe]
  );

  useEffect(() => {
    let phaseTimer: NodeJS.Timeout;

    const phases: AuthenticationPhase[] = [
      "initializing",
      "validating_session",
      "processing_credentials",
      "fetching_user_data",
      "preparing_dashboard",
      "redirecting",
    ];
    let currentPhaseIndex = 0;

    // Set initial operation text
    setCurrentOperationText(authPhases[phases[0]].operation);
    setProgress(authPhases[phases[0]].percentage);

    const advancePhase = () => {
      if (currentPhaseIndex < phases.length - 1) {
        currentPhaseIndex++;
        const newPhase = phases[currentPhaseIndex];
        setCurrentPhase(newPhase);
        onPhaseChange?.(newPhase);

        // Update progress and operation text
        setProgress(authPhases[newPhase].percentage);
        setCurrentOperationText(authPhases[newPhase].operation);

        // Vary timing based on phase complexity
        const phaseTimings = {
          initializing: 800,
          validating_session: 1200,
          processing_credentials: 1500,
          fetching_user_data: 2000,
          preparing_dashboard: 1000,
          redirecting: 800,
        };

        phaseTimer = setTimeout(advancePhase, phaseTimings[newPhase] || 1000);
      } else {
        // Final phase completed
        setTimeout(() => {
          onComplete?.();
        }, 500);
      }
    };

    // Start the authentication flow
    const initialTiming = isIframe ? 1000 : 800; // Slightly longer for iframe setup
    phaseTimer = setTimeout(advancePhase, initialTiming);

    return () => {
      clearTimeout(phaseTimer);
    };
  }, [isIframe, onPhaseChange, onComplete, authPhases]);

  const currentPhaseData = authPhases[currentPhase];
  const displayOperation = currentOperation || currentOperationText;

  return (
    <div className="text-white text-center max-w-md mx-auto">
      {/* Main Title */}
      <h1 className="text-2xl mb-6 font-semibold">{currentPhaseData.title}</h1>

      {/* Animated Icon */}
      <div className="mb-6 flex justify-center">
        <div className="relative">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 100 100"
            width="80"
            height="80"
            className="text-white"
          >
            <defs>
              <linearGradient
                id="authGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="currentColor" stopOpacity="0.8" />
                <stop
                  offset="100%"
                  stopColor="currentColor"
                  stopOpacity="0.3"
                />
              </linearGradient>
            </defs>

            {/* Outer rotating ring */}
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="url(#authGradient)"
              strokeWidth="2"
              strokeDasharray="8,4"
              opacity="0.6"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="0 50 50"
                to="360 50 50"
                dur="3s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Inner pulsing circle */}
            <circle cx="50" cy="50" r="25" fill="currentColor" opacity="0.2">
              <animate
                attributeName="r"
                values="20;30;20"
                dur="2s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="opacity"
                values="0.1;0.3;0.1"
                dur="2s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Center icon */}
            <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.8" />
          </svg>
        </div>
      </div>

      {/* Description */}
      <p className="text-lg mb-6 text-white/90 leading-relaxed">
        {currentPhaseData.description}
      </p>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="w-full bg-white/20 h-2 rounded-full overflow-hidden">
          <div
            className="bg-white h-full transition-all duration-300 ease-out rounded-full"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Progress Info */}
      <div className="flex justify-center items-center text-sm text-white/70 mb-4">
        <span>{Math.round(progress)}% complete</span>
      </div>

      {/* Current Operation Indicator */}
      <div className="text-xs text-white/60 uppercase tracking-wider mb-4">
        {currentPhase
          .replace(/_/g, " ")
          .replace(/([A-Z])/g, " $1")
          .trim()}
      </div>

      {/* Operation Status */}
      <div className="text-sm text-white/80 mb-4 font-medium">
        {displayOperation}
      </div>

      {/* Contextual Tips based on current phase */}
      {currentPhase === "initializing" && (
        <div className="text-xs text-white/50 italic">
          {isIframe
            ? "Establishing secure connection with parent application..."
            : "Setting up your authentication environment..."}
        </div>
      )}

      {currentPhase === "validating_session" && (
        <div className="text-xs text-white/50 italic">
          {isIframe
            ? "Processing authentication tokens from parent application..."
            : "Verifying your existing session credentials..."}
        </div>
      )}

      {currentPhase === "processing_credentials" && (
        <div className="text-xs text-white/50 italic">
          Establishing secure session with AWS Cognito...
        </div>
      )}

      {currentPhase === "fetching_user_data" && (
        <div className="text-xs text-white/50 italic">
          Loading your application details and user preferences...
        </div>
      )}

      {currentPhase === "preparing_dashboard" && (
        <div className="text-xs text-white/50 italic">
          Configuring your personalized application interface...
        </div>
      )}
    </div>
  );
};

export default DynamicAuthProgress;
